version: '3.8'

services:
  # Redis (Message Queue & Cache)
  redis:
    image: redis:7-alpine
    container_name: text-classification-redis-prod
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - app-network

  # Backend API
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: text-classification-backend-prod
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./data/text_classification.db
      - REDIS_URL=redis://redis:6379/0
      - PYTHONPATH=/app
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - SECRET_KEY=${SECRET_KEY}
      - ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      - DEBUG=False
      - WORKER_MODE=false
    volumes:
      - backend_data:/app/data
      - backend_logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - app-network
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # RQ Workers for background processing (scaled)
  worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=sqlite:///./data/text_classification.db
      - REDIS_URL=redis://redis:6379/0
      - PYTHONPATH=/app
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - SECRET_KEY=${SECRET_KEY}
      - WORKER_MODE=true
      - DEBUG=False
    volumes:
      - backend_data:/app/data
      - backend_logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
      backend:
        condition: service_healthy
    restart: always
    command: ["python", "start_workers.py", "--mode", "docker"]
    healthcheck:
      test: ["CMD", "python", "-c", "import redis; r=redis.from_url('redis://redis:6379/0'); r.ping()"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - app-network
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # RQ Dashboard (monitoring)
  rq-dashboard:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: text-classification-rq-dashboard-prod
    ports:
      - "9181:9181"
    environment:
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      redis:
        condition: service_healthy
    restart: always
    command: ["rq-dashboard", "--redis-url", "redis://redis:6379/0", "--port", "9181", "--bind", "0.0.0.0"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9181/"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - app-network

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: text-classification-frontend-prod
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      backend:
        condition: service_healthy
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - app-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

volumes:
  backend_data:
    driver: local
  backend_logs:
    driver: local
  redis_data:
    driver: local

networks:
  app-network:
    driver: bridge
    name: text-classification-prod-network 