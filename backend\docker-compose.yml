version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: text_classification_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-rootpassword}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-text_classification}
      MYSQL_USER: ${MYSQL_USER:-app_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-app_password}
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql-init:/docker-entrypoint-initdb.d
    command: [
      '--character-set-server=utf8mb4',
      '--collation-server=utf8mb4_unicode_ci',
      '--innodb-buffer-pool-size=512M',
      '--max-connections=200',
      '--innodb-flush-log-at-trx-commit=2',
      '--innodb-log-file-size=128M',
      '--slow-query-log=1',
      '--slow-query-log-file=/var/log/mysql/slow.log',
      '--long-query-time=2'
    ]
    networks:
      - text_classification_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-rootpassword}"]
      timeout: 20s
      retries: 10

  redis:
    image: redis:7-alpine
    container_name: text_classification_redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    networks:
      - text_classification_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 10s
      retries: 5

  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: text_classification_api
    restart: unless-stopped
    environment:
      - DB_TYPE=mysql
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USER=${MYSQL_USER:-app_user}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-app_password}
      - MYSQL_DATABASE=${MYSQL_DATABASE:-text_classification}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SECRET_KEY=${SECRET_KEY:-your_super_secret_key_here}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY:-}
    ports:
      - "${API_PORT:-8000}:8000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - text_classification_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      timeout: 10s
      retries: 3
      start_period: 60s

  worker-classification:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: text_classification_worker_classification
    restart: unless-stopped
    environment:
      - DB_TYPE=mysql
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USER=${MYSQL_USER:-app_user}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-app_password}
      - MYSQL_DATABASE=${MYSQL_DATABASE:-text_classification}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SECRET_KEY=${SECRET_KEY:-your_super_secret_key_here}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY:-}
    command: ["python", "worker.py", "classification"]
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - text_classification_network

  worker-batch:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: text_classification_worker_batch
    restart: unless-stopped
    environment:
      - DB_TYPE=mysql
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USER=${MYSQL_USER:-app_user}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-app_password}
      - MYSQL_DATABASE=${MYSQL_DATABASE:-text_classification}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SECRET_KEY=${SECRET_KEY:-your_super_secret_key_here}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY:-}
    command: ["python", "worker.py", "batch_processing"]
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - text_classification_network

  worker-csv:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: text_classification_worker_csv
    restart: unless-stopped
    environment:
      - DB_TYPE=mysql
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USER=${MYSQL_USER:-app_user}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-app_password}
      - MYSQL_DATABASE=${MYSQL_DATABASE:-text_classification}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SECRET_KEY=${SECRET_KEY:-your_super_secret_key_here}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY:-}
    command: ["python", "worker.py", "csv_processing"]
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - text_classification_network

  adminer:
    image: adminer:latest
    container_name: text_classification_adminer
    restart: unless-stopped
    ports:
      - "${ADMINER_PORT:-8080}:8080"
    environment:
      ADMINER_DEFAULT_SERVER: mysql
      ADMINER_DESIGN: hydra
    networks:
      - text_classification_network
    depends_on:
      - mysql

  rq-dashboard:
    image: eoranged/rq-dashboard
    container_name: text_classification_rq_dashboard
    restart: unless-stopped
    environment:
      RQ_DASHBOARD_REDIS_URL: redis://redis:6379/0
    ports:
      - "${RQ_DASHBOARD_PORT:-9181}:9181"
    depends_on:
      - redis
    networks:
      - text_classification_network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  text_classification_network:
    driver: bridge 