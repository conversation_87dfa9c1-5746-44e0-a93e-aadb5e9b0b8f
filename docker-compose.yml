version: '3.8'

services:
  # Redis (Message Queue & Cache)
  redis:
    image: redis:7-alpine
    container_name: text-classification-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: text-classification-backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./data/text_classification.db
      - REDIS_URL=redis://redis:6379/0
      - PYTHONPATH=/app
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
    volumes:
      - ./backend:/app
      - backend_data:/app/data
      - backend_logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # RQ Workers for background processing
  worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: text-classification-worker
    environment:
      - DATABASE_URL=sqlite:///./data/text_classification.db
      - REDIS_URL=redis://redis:6379/0
      - PYTHONPATH=/app
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - WORKER_MODE=true
    volumes:
      - ./backend:/app
      - backend_data:/app/data
      - backend_logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
      backend:
        condition: service_healthy
    restart: unless-stopped
    command: ["python", "start_workers.py", "--mode", "docker"]
    healthcheck:
      test: ["CMD", "python", "-c", "import redis; r=redis.from_url('redis://redis:6379/0'); r.ping()"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RQ Dashboard (optional, for monitoring)
  rq-dashboard:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: text-classification-rq-dashboard
    ports:
      - "9181:9181"
    environment:
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: ["rq-dashboard", "--redis-url", "redis://redis:6379/0", "--port", "9181", "--bind", "0.0.0.0"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9181/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: text-classification-frontend
    ports:
      - "3000:80"
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  backend_data:
    driver: local
  backend_logs:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    name: text-classification-network
    driver: bridge
