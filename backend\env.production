# Production Environment Configuration

# Database Configuration
DB_TYPE=mysql
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USER=app_user
MYSQL_PASSWORD=change_this_password_in_production
MYSQL_DATABASE=text_classification
MYSQL_ROOT_PASSWORD=change_this_root_password_in_production

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# JWT Configuration
SECRET_KEY=change_this_secret_key_in_production_minimum_32_characters
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Google Gemini API (optional)
GOOGLE_API_KEY=your_google_api_key_here

# Application Settings
DEBUG=False
LOG_LEVEL=INFO

# Port Configuration
API_PORT=8000
MYSQL_PORT=3306
REDIS_PORT=6379
ADMINER_PORT=8080
RQ_DASHBOARD_PORT=9181

# Security Settings (for production)
CORS_ORIGINS=["https://yourdomain.com", "https://api.yourdomain.com"]
ALLOWED_HOSTS=["yourdomain.com", "api.yourdomain.com"] 