# Environment Variables Configuration
# Copy this file to .env and update the values

# =================================
# API KEYS
# =================================
# Google Gemini API Key for translation and language processing
GEMINI_API_KEY=your_gemini_api_key_here

# =================================
# SECURITY
# =================================
# JWT Secret Key - Generate a strong random string
SECRET_KEY=your-super-secret-jwt-key-here-make-it-long-and-random

# JWT Algorithm
ALGORITHM=HS256

# Token expiration time in minutes
ACCESS_TOKEN_EXPIRE_MINUTES=30

# =================================
# DATABASE
# =================================
# Database URL for development (SQLite)
DATABASE_URL=sqlite:///./data/text_classification.db

# For production, you might want to use PostgreSQL:
# DATABASE_URL=postgresql://user:password@host:port/database_name

# =================================
# REDIS CONFIGURATION
# =================================
# Redis URL for queue and caching
REDIS_URL=redis://localhost:6379/0

# For Docker, Redis URL will be:
# REDIS_URL=redis://redis:6379/0

# =================================
# APPLICATION SETTINGS
# =================================
# Debug mode (set to False in production)
DEBUG=True

# Allowed hosts (comma-separated for production)
ALLOWED_HOSTS=localhost,127.0.0.1

# =================================
# QUEUE SETTINGS
# =================================
# Worker mode (set to true for worker containers)
WORKER_MODE=false

# =================================
# DOCKER SETTINGS
# =================================
# Python path for Docker containers
PYTHONPATH=/app

# Unbuffered Python output
PYTHONUNBUFFERED=1 